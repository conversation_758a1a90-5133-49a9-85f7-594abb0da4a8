#!/bin/bash
#
# Sample submission script for running ORB feature extraction in parallel
#
#SBATCH --job-name=mattervial_getfeats     # Name your job
#SBATCH --output=mattervial_getfeats_%A.out  # Output file (job ID and array task ID appended)
#SBATCH --error=mattervial_getfeats_%A.err   # Error file
#SBATCH --partition=debug-gpu            # Debug GPU partition for testing (or 'gpu' for production)
#SBATCH --nodes=1                        # Request one node per task
#SBATCH --ntasks-per-node=1              # One task (serial job) per node
#SBATCH --mem=120G                       # Total memory on the node (adjust if needed)
#SBATCH --gpus=1                         # Request one GPU per task
#SBATCH --time=01:00:00                  # Maximum walltime (2h for debug jobs, adjust for production)
#SBATCH --account=htforft                # Replace with your actual project/account name

echo "Job started on $(date)"
echo "Running on node(s): $SLURM_NODELIST"
echo "SLURM_JOB_ID: $SLURM_JOB_ID"
echo "SLURM_ARRAY_TASK_ID: $SLURM_ARRAY_TASK_ID"


# Activate your conda environment (adjust the path and environment name as needed)
source /gpfs/home/<USER>/ucl-modl/rgouvea/miniconda3/etc/profile.d/conda.sh
# conda activate /gpfs/scratch/acad/htforft/rgouvea/orb_env
conda activate ML39
# module purge
# module load EasyBuild/2022a CUDA/11.7.0 cuDNN/8.4.1.50-CUDA-11.7.0

export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1
export NUMEXPR_NUM_THREADS=1
export VECLIB_MAXIMUM_THREADS=1

python3 -u run_gpu.py

echo "Job finished on $(date)"